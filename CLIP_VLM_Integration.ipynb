# Clone the CLIP model repository
!git clone https://github.com/Matheus-Rezendee/Modelo-CLIP.git
print("✅ Repository cloned successfully!")

# Install required dependencies for CLIP-GmP-ViT-L-14
!pip install transformers torch torchvision numpy matplotlib pillow opencv-python scikit-learn
!pip install --upgrade huggingface_hub
print("✅ Dependencies installed successfully!")

# Import necessary libraries
import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import cv2
from transformers import CLIPProcessor, CLIPModel
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

print("📦 All libraries imported successfully!")
print(f"🔥 PyTorch version: {torch.__version__}")
print(f"💻 CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")

class CLIPVisionLanguageModel:
    """
    CLIP Vision-Language Model wrapper for multimodal processing
    Supports both GPU and CPU inference with proper error handling
    """
    
    def __init__(self, model_name="zer0int/CLIP-GmP-ViT-L-14", device="auto"):
        """
        Initialize the CLIP model
        
        Args:
            model_name (str): HuggingFace model identifier
            device (str): Device to use ('auto', 'cuda', 'cpu')
        """
        self.model_name = model_name
        self.device = self._setup_device(device)
        self.model = None
        self.processor = None
        self.is_loaded = False
        
    def _setup_device(self, device):
        """Setup the appropriate device for inference"""
        if device == "auto":
            if torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        return device
    
    def load_model(self):
        """
        Load the CLIP model and processor with error handling
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print(f"🤖 Loading CLIP model: {self.model_name}")
            print(f"💻 Using device: {self.device}")
            
            # Load processor
            self.processor = CLIPProcessor.from_pretrained(self.model_name)
            print("✅ Processor loaded successfully")
            
            # Load model
            self.model = CLIPModel.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()  # Set to evaluation mode
            print("✅ Model loaded successfully")
            
            self.is_loaded = True
            return True
            
        except Exception as e:
            print(f"❌ Error loading model: {str(e)}")
            print("💡 Trying alternative loading method...")
            return self._load_model_fallback()
    
    def _load_model_fallback(self):
        """Fallback loading method with different configurations"""
        try:
            # Try with local files only
            self.processor = CLIPProcessor.from_pretrained(
                self.model_name, 
                local_files_only=False,
                trust_remote_code=True
            )
            
            self.model = CLIPModel.from_pretrained(
                self.model_name,
                local_files_only=False,
                trust_remote_code=True,
                torch_dtype=torch.float32
            )
            
            self.model.to(self.device)
            self.model.eval()
            self.is_loaded = True
            print("✅ Model loaded with fallback method")
            return True
            
        except Exception as e:
            print(f"❌ Fallback loading failed: {str(e)}")
            self.is_loaded = False
            return False

# Initialize the CLIP model
print("🚀 Initializing CLIP Vision-Language Model...")
clip_model = CLIPVisionLanguageModel()
success = clip_model.load_model()

if success:
    print("\n🎉 CLIP model ready for inference!")
else:
    print("\n⚠️ Model loading failed. Please check your internet connection and try again.")

def process_image_text_pair(image_input, text_input, return_similarities=True):
    """
    Process an image-text pair using CLIP model
    
    Args:
        image_input: PIL Image, numpy array, or path to image file
        text_input: String or list of strings
        return_similarities: Whether to return similarity scores
    
    Returns:
        dict: Processing results with similarities and embeddings
    """
    if not clip_model.is_loaded:
        return {"error": "Model not loaded. Please load the model first."}
    
    try:
        # Handle different image input types
        if isinstance(image_input, str):
            # Path to image file
            image = Image.open(image_input).convert('RGB')
        elif isinstance(image_input, np.ndarray):
            # Numpy array
            image = Image.fromarray(image_input).convert('RGB')
        else:
            # Assume PIL Image
            image = image_input.convert('RGB')
        
        # Handle text input
        if isinstance(text_input, str):
            texts = [text_input]
        else:
            texts = text_input
        
        # Process inputs
        inputs = clip_model.processor(
            text=texts, 
            images=image, 
            return_tensors="pt", 
            padding=True
        )
        
        # Move to device
        inputs = {k: v.to(clip_model.device) for k, v in inputs.items()}
        
        # Get model outputs
        with torch.no_grad():
            outputs = clip_model.model(**inputs)
        
        results = {
            "success": True,
            "image_features": outputs.image_embeds.cpu().numpy(),
            "text_features": outputs.text_embeds.cpu().numpy(),
        }
        
        if return_similarities:
            # Calculate similarities
            logits_per_image = outputs.logits_per_image
            probs = logits_per_image.softmax(dim=1)
            
            results["similarities"] = logits_per_image.cpu().numpy()
            results["probabilities"] = probs.cpu().numpy()
            results["text_labels"] = texts
        
        return results
        
    except Exception as e:
        return {"error": f"Processing failed: {str(e)}"}

def find_best_text_match(image_input, text_candidates):
    """
    Find the best matching text description for an image
    
    Args:
        image_input: Image input (various formats supported)
        text_candidates: List of text descriptions to match against
    
    Returns:
        dict: Best match results
    """
    results = process_image_text_pair(image_input, text_candidates)
    
    if "error" in results:
        return results
    
    # Find best match
    similarities = results["similarities"][0]  # First (and only) image
    best_idx = np.argmax(similarities)
    
    return {
        "best_match": text_candidates[best_idx],
        "best_score": float(similarities[best_idx]),
        "all_scores": {text: float(score) for text, score in zip(text_candidates, similarities)},
        "confidence": float(results["probabilities"][0][best_idx])
    }

print("✅ Core pipeline functions defined successfully!")

def extract_video_frames(video_path, max_frames=10, frame_interval=None):
    """
    Extract frames from video for CLIP processing
    
    Args:
        video_path: Path to video file
        max_frames: Maximum number of frames to extract
        frame_interval: Interval between frames (if None, evenly distributed)
    
    Returns:
        list: List of PIL Images
    """
    try:
        cap = cv2.VideoCapture(video_path)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        if frame_interval is None:
            # Evenly distribute frames
            frame_indices = np.linspace(0, total_frames-1, max_frames, dtype=int)
        else:
            # Use specified interval
            frame_indices = list(range(0, min(total_frames, max_frames * frame_interval), frame_interval))
        
        frames = []
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret:
                # Convert BGR to RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(frame_rgb)
                frames.append(pil_image)
        
        cap.release()
        return frames
        
    except Exception as e:
        print(f"❌ Error extracting frames: {str(e)}")
        return []

def analyze_video_content(video_path, text_queries, max_frames=5):
    """
    Analyze video content using CLIP model
    
    Args:
        video_path: Path to video file
        text_queries: List of text descriptions to match against
        max_frames: Number of frames to analyze
    
    Returns:
        dict: Analysis results
    """
    print(f"🎬 Analyzing video: {os.path.basename(video_path)}")
    
    # Extract frames
    frames = extract_video_frames(video_path, max_frames)
    if not frames:
        return {"error": "Failed to extract frames from video"}
    
    print(f"📸 Extracted {len(frames)} frames")
    
    # Analyze each frame
    frame_results = []
    overall_scores = {query: [] for query in text_queries}
    
    for i, frame in enumerate(frames):
        print(f"🔍 Analyzing frame {i+1}/{len(frames)}...")
        
        result = find_best_text_match(frame, text_queries)
        if "error" not in result:
            frame_results.append({
                "frame_index": i,
                "best_match": result["best_match"],
                "confidence": result["confidence"],
                "all_scores": result["all_scores"]
            })
            
            # Accumulate scores
            for query in text_queries:
                overall_scores[query].append(result["all_scores"][query])
    
    # Calculate overall video analysis
    video_analysis = {}
    for query in text_queries:
        scores = overall_scores[query]
        if scores:
            video_analysis[query] = {
                "average_score": np.mean(scores),
                "max_score": np.max(scores),
                "min_score": np.min(scores),
                "std_score": np.std(scores)
            }
    
    # Find best overall match
    avg_scores = {query: video_analysis[query]["average_score"] for query in video_analysis}
    best_overall = max(avg_scores.items(), key=lambda x: x[1]) if avg_scores else (None, 0)
    
    return {
        "video_path": video_path,
        "frames_analyzed": len(frames),
        "frame_results": frame_results,
        "video_analysis": video_analysis,
        "best_overall_match": best_overall[0],
        "best_overall_score": best_overall[1]
    }

print("✅ Video processing functions defined successfully!")

# Test with sample image (create a simple test image if no image available)
def create_test_image():
    """Create a simple test image for demonstration"""
    # Create a simple colored image
    img_array = np.zeros((224, 224, 3), dtype=np.uint8)
    img_array[:, :112] = [255, 0, 0]  # Red half
    img_array[:, 112:] = [0, 0, 255]  # Blue half
    return Image.fromarray(img_array)

# Test the model with a simple example
if clip_model.is_loaded:
    print("🧪 Testing CLIP model with sample data...")
    
    # Create test image
    test_image = create_test_image()
    
    # Test descriptions
    test_descriptions = [
        "a red and blue image",
        "a colorful geometric pattern",
        "a photograph of a cat",
        "a landscape with mountains",
        "abstract colored shapes"
    ]
    
    # Run test
    result = find_best_text_match(test_image, test_descriptions)
    
    if "error" not in result:
        print("\n🎉 Model test successful!")
        print(f"🏆 Best match: '{result['best_match']}'")
        print(f"📊 Confidence: {result['confidence']:.3f}")
        print("\n📈 All scores:")
        for desc, score in result['all_scores'].items():
            print(f"  • {desc}: {score:.3f}")
    else:
        print(f"❌ Test failed: {result['error']}")
        
    # Display test image
    plt.figure(figsize=(6, 6))
    plt.imshow(test_image)
    plt.title(f"Test Image\nBest Match: {result.get('best_match', 'N/A')}")
    plt.axis('off')
    plt.show()
    
else:
    print("⚠️ Model not loaded. Please run the model loading cell first.")

# Example 1: Image Classification
def classify_image(image_path, categories):
    """
    Classify an image into predefined categories
    
    Args:
        image_path: Path to image file
        categories: List of category names
    
    Returns:
        dict: Classification results
    """
    if not clip_model.is_loaded:
        return {"error": "Model not loaded"}
    
    # Create text prompts for categories
    text_prompts = [f"a photo of {category}" for category in categories]
    
    result = find_best_text_match(image_path, text_prompts)
    
    if "error" not in result:
        # Extract category from best match
        best_category = result["best_match"].replace("a photo of ", "")
        return {
            "predicted_category": best_category,
            "confidence": result["confidence"],
            "all_categories": {cat: result["all_scores"][f"a photo of {cat}"] for cat in categories}
        }
    
    return result

# Example 2: Content Moderation
def moderate_content(image_input, safety_checks=None):
    """
    Perform content moderation on an image
    
    Args:
        image_input: Image input (various formats)
        safety_checks: List of safety-related descriptions
    
    Returns:
        dict: Moderation results
    """
    if safety_checks is None:
        safety_checks = [
            "safe and appropriate content",
            "inappropriate or harmful content",
            "violent or disturbing imagery",
            "adult or explicit content"
        ]
    
    result = find_best_text_match(image_input, safety_checks)
    
    if "error" not in result:
        is_safe = "safe and appropriate" in result["best_match"]
        return {
            "is_safe": is_safe,
            "safety_assessment": result["best_match"],
            "confidence": result["confidence"],
            "detailed_scores": result["all_scores"]
        }
    
    return result

# Example 3: Scene Understanding
def understand_scene(image_input):
    """
    Understand the scene in an image
    
    Args:
        image_input: Image input (various formats)
    
    Returns:
        dict: Scene understanding results
    """
    scene_types = [
        "indoor scene", "outdoor scene", "urban environment", "natural landscape",
        "office or workplace", "home or residential", "restaurant or cafe",
        "street or road", "park or garden", "beach or waterfront"
    ]
    
    objects = [
        "people or persons", "vehicles or cars", "buildings or architecture",
        "animals or pets", "food or meals", "technology or devices",
        "furniture or objects", "plants or vegetation", "sports or activities"
    ]
    
    # Analyze scene type
    scene_result = find_best_text_match(image_input, scene_types)
    
    # Analyze objects
    object_result = find_best_text_match(image_input, objects)
    
    if "error" not in scene_result and "error" not in object_result:
        return {
            "scene_type": scene_result["best_match"],
            "scene_confidence": scene_result["confidence"],
            "main_objects": object_result["best_match"],
            "object_confidence": object_result["confidence"],
            "scene_scores": scene_result["all_scores"],
            "object_scores": object_result["all_scores"]
        }
    
    return {"error": "Scene analysis failed"}

print("✅ Practical usage examples defined successfully!")
print("\n🚀 CLIP VLM Integration Pipeline Ready!")
print("\n📋 Available functions:")
print("  • process_image_text_pair() - Core CLIP processing")
print("  • find_best_text_match() - Find best text match for image")
print("  • analyze_video_content() - Video analysis with CLIP")
print("  • classify_image() - Image classification")
print("  • moderate_content() - Content moderation")
print("  • understand_scene() - Scene understanding")
print("\n💡 Ready to process your images and videos!")