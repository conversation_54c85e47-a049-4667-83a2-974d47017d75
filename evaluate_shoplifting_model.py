#!/usr/bin/env python3
"""
Shoplifting Detection Model Evaluation - F1 Score Calculator
Evaluates the model on all videos in both datasets and calculates performance metrics
"""

import os
import sys
import time
import json
from test_video_model import VideoModelTester
from sklearn.metrics import f1_score, precision_score, recall_score, confusion_matrix, classification_report

def main():
    print("📊 SHOPLIFTING DETECTION MODEL EVALUATION")
    print("=" * 80)
    print("🎯 Testing all videos in both datasets")
    print("📈 Calculating F1 Score and performance metrics")
    print("=" * 80)
    
    # Dataset paths
    theft_dir = r"C:\Users\<USER>\Desktop\video pour mohamed\video vol"
    normal_dir = r"C:\Users\<USER>\Desktop\video pour mohamed\video normale"
    
    # Collect all videos
    theft_videos = []
    normal_videos = []
    
    print("📁 Collecting videos from datasets...")
    
    # Collect theft videos (positive class)
    if os.path.exists(theft_dir):
        for file in os.listdir(theft_dir):
            if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                theft_videos.append(os.path.join(theft_dir, file))
    
    # Collect normal videos (negative class)
    if os.path.exists(normal_dir):
        for file in os.listdir(normal_dir):
            if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                normal_videos.append(os.path.join(normal_dir, file))
    
    print(f"🚨 Theft videos (positive class): {len(theft_videos)}")
    print(f"✅ Normal videos (negative class): {len(normal_videos)}")
    print(f"📊 Total videos to evaluate: {len(theft_videos) + len(normal_videos)}")
    
    if len(theft_videos) == 0 or len(normal_videos) == 0:
        print("❌ Need both theft and normal videos for evaluation")
        return
    
    # Initialize detector
    print("\n🤖 Initializing shoplifting detection model...")
    detector = VideoModelTester(".")
    
    if not detector.load_model():
        print("❌ Failed to initialize detector")
        return
    
    print("✅ Model loaded successfully!")
    
    # Evaluation data
    y_true = []  # Ground truth labels
    y_pred = []  # Predicted labels
    results = []  # Detailed results
    
    total_videos = len(theft_videos) + len(normal_videos)
    current_video = 0
    
    print(f"\n{'='*80}")
    print(f"🔍 EVALUATING THEFT VIDEOS (Positive Class)")
    print(f"{'='*80}")
    
    # Evaluate theft videos (should be detected as shoplifting)
    for i, video_path in enumerate(theft_videos, 1):
        current_video += 1
        print(f"\n[{current_video}/{total_videos}] 🚨 Theft Video {i}: {os.path.basename(video_path)}")
        
        start_time = time.time()
        response = detector.test_video(video_path, "Is there shoplifting in this video?")
        end_time = time.time()
        
        # Ground truth: 1 (theft video)
        y_true.append(1)
        
        # Predict based on response
        if response:
            if "POTENTIAL SHOPLIFTING DETECTED" in response:
                prediction = 1  # High confidence positive
                confidence = "HIGH"
            elif "REQUIRES REVIEW" in response:
                prediction = 1  # Medium confidence positive
                confidence = "MEDIUM"
            else:  # "NO SHOPLIFTING DETECTED"
                prediction = 0  # Negative
                confidence = "LOW"
        else:
            prediction = 0  # Failed analysis
            confidence = "FAILED"
        
        y_pred.append(prediction)
        
        result = {
            'video': os.path.basename(video_path),
            'type': 'theft',
            'ground_truth': 1,
            'prediction': prediction,
            'confidence': confidence,
            'response': response,
            'time': end_time - start_time
        }
        results.append(result)
        
        # Show result
        status = "✅ CORRECT" if prediction == 1 else "❌ MISSED"
        print(f"   Ground Truth: THEFT | Prediction: {'THEFT' if prediction == 1 else 'NORMAL'} | {status}")
        print(f"   Confidence: {confidence} | Time: {end_time-start_time:.2f}s")
    
    print(f"\n{'='*80}")
    print(f"✅ EVALUATING NORMAL VIDEOS (Negative Class)")
    print(f"{'='*80}")
    
    # Evaluate normal videos (should NOT be detected as shoplifting)
    for i, video_path in enumerate(normal_videos, 1):
        current_video += 1
        print(f"\n[{current_video}/{total_videos}] ✅ Normal Video {i}: {os.path.basename(video_path)}")
        
        start_time = time.time()
        response = detector.test_video(video_path, "Is there shoplifting in this video?")
        end_time = time.time()
        
        # Ground truth: 0 (normal video)
        y_true.append(0)
        
        # Predict based on response
        if response:
            if "POTENTIAL SHOPLIFTING DETECTED" in response:
                prediction = 1  # False positive
                confidence = "HIGH"
            elif "REQUIRES REVIEW" in response:
                prediction = 1  # False positive
                confidence = "MEDIUM"
            else:  # "NO SHOPLIFTING DETECTED"
                prediction = 0  # Correct negative
                confidence = "LOW"
        else:
            prediction = 0  # Failed analysis
            confidence = "FAILED"
        
        y_pred.append(prediction)
        
        result = {
            'video': os.path.basename(video_path),
            'type': 'normal',
            'ground_truth': 0,
            'prediction': prediction,
            'confidence': confidence,
            'response': response,
            'time': end_time - start_time
        }
        results.append(result)
        
        # Show result
        status = "✅ CORRECT" if prediction == 0 else "❌ FALSE POSITIVE"
        print(f"   Ground Truth: NORMAL | Prediction: {'THEFT' if prediction == 1 else 'NORMAL'} | {status}")
        print(f"   Confidence: {confidence} | Time: {end_time-start_time:.2f}s")
    
    # Calculate metrics
    print(f"\n{'='*80}")
    print(f"📊 PERFORMANCE METRICS CALCULATION")
    print(f"{'='*80}")
    
    # Calculate all metrics
    f1 = f1_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    
    # Confusion matrix
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    
    # Additional metrics
    accuracy = (tp + tn) / (tp + tn + fp + fn)
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    # Calculate average processing time
    avg_time = sum(r['time'] for r in results) / len(results)
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"{'='*50}")
    print(f"📈 F1 SCORE:           {f1:.4f} ({f1*100:.2f}%)")
    print(f"🎯 PRECISION:          {precision:.4f} ({precision*100:.2f}%)")
    print(f"🔍 RECALL:             {recall:.4f} ({recall*100:.2f}%)")
    print(f"✅ ACCURACY:           {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"🛡️ SPECIFICITY:        {specificity:.4f} ({specificity*100:.2f}%)")
    print(f"⏱️ AVG PROCESSING TIME: {avg_time:.2f} seconds/video")
    
    print(f"\n📊 CONFUSION MATRIX:")
    print(f"{'='*30}")
    print(f"                 Predicted")
    print(f"               Normal  Theft")
    print(f"Actual Normal    {tn:3d}    {fp:3d}")
    print(f"       Theft     {fn:3d}    {tp:3d}")
    
    print(f"\n📋 DETAILED BREAKDOWN:")
    print(f"{'='*40}")
    print(f"✅ True Positives (TP):  {tp:3d} - Theft correctly detected")
    print(f"✅ True Negatives (TN):  {tn:3d} - Normal correctly identified")
    print(f"❌ False Positives (FP): {fp:3d} - Normal wrongly flagged as theft")
    print(f"❌ False Negatives (FN): {fn:3d} - Theft missed")
    
    # Performance interpretation
    print(f"\n🎯 PERFORMANCE INTERPRETATION:")
    print(f"{'='*50}")
    if f1 >= 0.9:
        print(f"🏆 EXCELLENT: F1 Score ≥ 90% - Production ready!")
    elif f1 >= 0.8:
        print(f"🥇 VERY GOOD: F1 Score ≥ 80% - Good for deployment")
    elif f1 >= 0.7:
        print(f"🥈 GOOD: F1 Score ≥ 70% - Needs minor improvements")
    elif f1 >= 0.6:
        print(f"🥉 FAIR: F1 Score ≥ 60% - Needs significant improvements")
    else:
        print(f"⚠️ POOR: F1 Score < 60% - Major improvements needed")
    
    # Save detailed results
    results_file = "shoplifting_evaluation_results.json"
    evaluation_summary = {
        'metrics': {
            'f1_score': f1,
            'precision': precision,
            'recall': recall,
            'accuracy': accuracy,
            'specificity': specificity
        },
        'confusion_matrix': {
            'true_positives': int(tp),
            'true_negatives': int(tn),
            'false_positives': int(fp),
            'false_negatives': int(fn)
        },
        'dataset_info': {
            'theft_videos': len(theft_videos),
            'normal_videos': len(normal_videos),
            'total_videos': len(results)
        },
        'detailed_results': results
    }
    
    with open(results_file, 'w') as f:
        json.dump(evaluation_summary, f, indent=2)
    
    print(f"\n💾 RESULTS SAVED:")
    print(f"📄 Detailed results saved to: {results_file}")
    print(f"📊 Total videos evaluated: {len(results)}")
    print(f"⏱️ Total evaluation time: {sum(r['time'] for r in results):.2f} seconds")
    
    print(f"\n🚀 EVALUATION COMPLETE!")

if __name__ == "__main__":
    main()
